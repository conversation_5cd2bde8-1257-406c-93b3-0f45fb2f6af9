#!/usr/bin/env python3
"""
测试修复后的环境
"""

def test_imports():
    """测试关键组件是否能正常导入"""
    try:
        # 测试huggingface_hub的cached_download
        from huggingface_hub import cached_download
        print("✓ cached_download 可以正常导入")
    except ImportError as e:
        print(f"✗ cached_download 导入失败: {e}")
        return False
    
    try:
        # 测试sentence_transformers
        import sentence_transformers
        print("✓ sentence_transformers 可以正常导入")
    except ImportError as e:
        print(f"✗ sentence_transformers 导入失败: {e}")
        return False
        
    try:
        # 测试SentenceTransformer
        from sentence_transformers import SentenceTransformer
        print("✓ SentenceTransformer 可以正常导入")
    except ImportError as e:
        print(f"✗ SentenceTransformer 导入失败: {e}")
        return False
        
    return True

def test_model_loading():
    """测试模型加载"""
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')
        print("✓ 检索模型可以正常加载")
        return True
    except Exception as e:
        print(f"✗ 检索模型加载失败: {e}")
        return False

if __name__ == "__main__":
    print("测试环境修复情况...")
    print("=" * 30)
    
    if test_imports():
        print("\n导入测试通过!")
        if test_model_loading():
            print("\n模型加载测试通过!")
            print("\n环境已成功修复，可以正常使用项目!")
        else:
            print("\n模型加载测试失败!")
    else:
        print("\n导入测试失败!")